import {
  collection,
  doc,
  getDoc,
  getDocs,
  setDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  Timestamp,
  writeBatch,
  increment
} from 'firebase/firestore';
import { 
  createUserWithEmailAndPassword,
  updateProfile,
  deleteUser as deleteAuthUser,
  sendPasswordResetEmail
} from 'firebase/auth';
import { auth, db } from './firebase';
import type { UserProfile, UserRole } from '../types/firebase';

export interface UserStats {
  totalUsers: number;
  totalStudents: number;
  totalDoctors: number;
  totalAdmins: number;
  activeUsers: number;
  newUsersThisMonth: number;
}

export interface CreateUserData {
  email: string;
  password: string;
  displayName: string;
  role: UserRole;
  department?: string;
  specialty?: string;
  phone?: string;
  status?: 'active' | 'inactive' | 'suspended';
}

export interface UpdateUserData {
  displayName?: string;
  role?: UserRole;
  department?: string;
  specialty?: string;
  phone?: string;
  status?: 'active' | 'inactive' | 'suspended';
}

/**
 * Get user statistics for admin dashboard
 */
export const getUserStats = async (): Promise<UserStats> => {
  try {
    const usersRef = collection(db, 'users');
    
    // Get all users
    const allUsersSnapshot = await getDocs(usersRef);
    const allUsers = allUsersSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    
    // Calculate statistics
    const totalUsers = allUsers.length;
    const totalStudents = allUsers.filter(user => user.role === 'student').length;
    const totalDoctors = allUsers.filter(user => user.role === 'doctor').length;
    const totalAdmins = allUsers.filter(user => user.role === 'admin').length;
    const activeUsers = allUsers.filter(user => user.status !== 'inactive' && user.status !== 'suspended').length;
    
    // Calculate new users this month
    const currentDate = new Date();
    const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    const newUsersThisMonth = allUsers.filter(user => {
      const createdAt = user.createdAt?.toDate ? user.createdAt.toDate() : new Date(user.createdAt);
      return createdAt >= firstDayOfMonth;
    }).length;

    return {
      totalUsers,
      totalStudents,
      totalDoctors,
      totalAdmins,
      activeUsers,
      newUsersThisMonth
    };
  } catch (error) {
    console.error('Error fetching user stats:', error);
    throw error;
  }
};

/**
 * Get all users with optional filtering
 */
export const getUsers = async (
  role?: UserRole,
  limitCount: number = 50,
  lastDoc?: any
): Promise<{ users: UserProfile[], hasMore: boolean, lastDoc: any }> => {
  try {
    const usersRef = collection(db, 'users');
    let q = query(usersRef, orderBy('createdAt', 'desc'));
    
    if (role) {
      q = query(usersRef, where('role', '==', role), orderBy('createdAt', 'desc'));
    }
    
    if (limitCount) {
      q = query(q, limit(limitCount));
    }
    
    if (lastDoc) {
      q = query(q, startAfter(lastDoc));
    }
    
    const snapshot = await getDocs(q);
    const users = snapshot.docs.map(doc => ({
      uid: doc.id,
      ...doc.data()
    })) as UserProfile[];
    
    const hasMore = snapshot.docs.length === limitCount;
    const newLastDoc = snapshot.docs[snapshot.docs.length - 1];
    
    return { users, hasMore, lastDoc: newLastDoc };
  } catch (error) {
    console.error('Error fetching users:', error);
    throw error;
  }
};

/**
 * Get a single user by ID
 */
export const getUserById = async (userId: string): Promise<UserProfile | null> => {
  try {
    const userDoc = await getDoc(doc(db, 'users', userId));
    
    if (userDoc.exists()) {
      return {
        uid: userDoc.id,
        ...userDoc.data()
      } as UserProfile;
    }
    
    return null;
  } catch (error) {
    console.error('Error fetching user:', error);
    throw error;
  }
};

/**
 * Create a new user (admin function)
 */
export const createUser = async (userData: CreateUserData): Promise<UserProfile> => {
  try {
    // Create user in Firebase Auth
    const userCredential = await createUserWithEmailAndPassword(auth, userData.email, userData.password);
    const user = userCredential.user;
    
    // Update display name
    await updateProfile(user, { displayName: userData.displayName });
    
    // Create user profile in Firestore
    const userProfile: Omit<UserProfile, 'uid'> = {
      email: userData.email,
      displayName: userData.displayName,
      role: userData.role,
      department: userData.department || '',
      specialty: userData.specialty || '',
      phone: userData.phone || '',
      status: userData.status || 'active',
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    await setDoc(doc(db, 'users', user.uid), userProfile);
    
    // If creating a student, assign doctors
    if (userData.role === 'student') {
      await assignDoctorsToStudent(user.uid);
    }
    
    return {
      uid: user.uid,
      ...userProfile
    };
  } catch (error) {
    console.error('Error creating user:', error);
    throw error;
  }
};

/**
 * Update user data
 */
export const updateUser = async (userId: string, userData: UpdateUserData): Promise<void> => {
  try {
    const updateData = {
      ...userData,
      updatedAt: new Date()
    };
    
    await updateDoc(doc(db, 'users', userId), updateData);
  } catch (error) {
    console.error('Error updating user:', error);
    throw error;
  }
};

/**
 * Delete a user
 */
export const deleteUser = async (userId: string): Promise<void> => {
  try {
    // Delete user document from Firestore
    await deleteDoc(doc(db, 'users', userId));
    
    // Note: Deleting from Firebase Auth requires admin SDK on backend
    // For now, we'll just mark as deleted in Firestore
    console.log('User deleted from Firestore. Auth deletion requires backend implementation.');
  } catch (error) {
    console.error('Error deleting user:', error);
    throw error;
  }
};

/**
 * Assign doctors to a student
 */
const assignDoctorsToStudent = async (studentId: string): Promise<void> => {
  try {
    // Get available doctors
    const { users: doctors } = await getUsers('doctor', 10);
    
    // Mock doctors if not enough real ones
    const mockDoctors = [
      { id: 'mock1', name: 'Dr. Sarah Johnson', specialty: 'General Practice', department: 'Primary Care' },
      { id: 'mock2', name: 'Dr. Michael Chen', specialty: 'Mental Health', department: 'Counseling Services' },
      { id: 'mock3', name: 'Dr. Emily Rodriguez', specialty: 'Emergency Medicine', department: 'Emergency Care' },
      { id: 'mock4', name: 'Dr. David Kim', specialty: 'Internal Medicine', department: 'Internal Medicine' },
      { id: 'mock5', name: 'Dr. Lisa Thompson', specialty: 'Dermatology', department: 'Dermatology' }
    ];
    
    const allDoctors = doctors.length >= 3 ? doctors : [...doctors, ...mockDoctors.slice(0, 5 - doctors.length)];
    const assignedDoctors = allDoctors.slice(0, 5);
    
    await setDoc(doc(db, 'doctorAssignments', studentId), {
      doctors: assignedDoctors.map(doctor => ({
        id: doctor.uid || doctor.id,
        name: doctor.displayName || doctor.name,
        specialty: doctor.specialty || 'General Practice',
        department: doctor.department || 'Primary Care',
        assignedDate: new Date().toISOString()
      })),
      updatedAt: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error assigning doctors:', error);
    throw error;
  }
};

/**
 * Send password reset email
 */
export const sendUserPasswordReset = async (email: string): Promise<void> => {
  try {
    await sendPasswordResetEmail(auth, email);
  } catch (error) {
    console.error('Error sending password reset:', error);
    throw error;
  }
};

/**
 * Search users by name or email
 */
export const searchUsers = async (searchTerm: string, role?: UserRole): Promise<UserProfile[]> => {
  try {
    const usersRef = collection(db, 'users');
    let q = query(usersRef);
    
    if (role) {
      q = query(usersRef, where('role', '==', role));
    }
    
    const snapshot = await getDocs(q);
    const users = snapshot.docs.map(doc => ({
      uid: doc.id,
      ...doc.data()
    })) as UserProfile[];
    
    // Filter by search term (client-side filtering for simplicity)
    const filteredUsers = users.filter(user => 
      user.displayName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email?.toLowerCase().includes(searchTerm.toLowerCase())
    );
    
    return filteredUsers;
  } catch (error) {
    console.error('Error searching users:', error);
    throw error;
  }
};
